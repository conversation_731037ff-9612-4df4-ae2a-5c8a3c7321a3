import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
} from "react";
import { InputAdornment, FormControl, MenuItem } from "@mui/material";
import { CustomTextField } from "../custom/CustomTextField";
import { SocketContext } from "../../../helpers/context/socket";
import { debounce } from "lodash";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import { URL } from "../../../helpers/constant/Url";

const TagInTextField = ({
  suggestionData,
  value,
  onChange,
  setActiveInput,
  activeInput,
  compareDataForRemove,
  removeTextTag,
  mainDataTag,
}) => {
  const [focused, setFocused] = useState(false);
  const [suggestions, setSuggestions] = useState(suggestionData || []);
  const textareaRef = useRef(null);
  const socket = useContext(SocketContext);
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;

  const debouncedHandleChange = useCallback(
    debounce((inputValue) => {
      const trimmedValue = inputValue.trim();
      if (trimmedValue.length > 0) {
        const match = trimmedValue.match(/@?(\w+)$/);
        const searchText = match ? match[1] : trimmedValue;
        socket?.emit("search_user", {
          Authorization: token,
          search_text: searchText,
        });
      } else {
        setSuggestions([]);
      }
    }, 300),
    [socket, token]
  );

  const handleChange = useCallback(
    (e) => {
      const inputValue = e.target.value;
      onChange(inputValue);
      debouncedHandleChange(inputValue);
      handleRemovedNames(inputValue);
    },
    [debouncedHandleChange, onChange]
  );

  useEffect(() => {
    handleRemovedNames(value);
  }, [value]);

  useEffect(() => {
    const handleReceiveSuggestions = (response) => {
      if (response?.status && response?.data) {
        setSuggestions(response.data);
      } else {
        setSuggestions([]);
      }
    };

    socket?.on("search_user", handleReceiveSuggestions);
    return () => {
      socket?.off("search_user", handleReceiveSuggestions);
    };
  }, [socket]);

  const handleSelectSuggestion = (suggestion) => {
    const updatedValue = value.replace(/@?\w*$/, `@${suggestion?.name} `);
    onChange(updatedValue, suggestion);
    setSuggestions([]);
  };

  const handleClearText = () => {
    onChange("");
    setSuggestions([]);
  };

  function handleRemovedNames(value) {
    let identyFireIndex = [];
    console.log(compareDataForRemove, "camparedata");

    for (var i = 0; i < compareDataForRemove?.length; i++) {
      compareDataForRemove[i].name = compareDataForRemove[i].name?.trimEnd();
    }
    for (var i = 0; i < compareDataForRemove?.length; i++) {
      for (var j = 0; j < value?.length; j++) {
        if (compareDataForRemove[i]?.name[0] == value[j]) {
          for (var k = 0; k < compareDataForRemove[i].name.length; k++) {
            if (compareDataForRemove[i].name[k] == value[j + k]) {
              console.log(value[j + k], "de4");
              if (
                k == compareDataForRemove[i].name.length - 1 &&
                !identyFireIndex.includes(compareDataForRemove[i])
              ) {
                if (value[j + k + 2] == undefined || value[j + k + 1] == " ")
                  identyFireIndex[identyFireIndex.length] =
                    compareDataForRemove[i];
                removeTextTag(identyFireIndex);
                console.log(identyFireIndex);
              }
            } else {
              break;
            }
          }
        }
      }
    }
    console.log("identyFireIndex_2", identyFireIndex);
  }

  const showSuggestions =
    activeInput === "tag" && suggestions.length > 0 && value.trim().length > 0;

  return (
    <FormControl fullWidth variant="outlined">
      <CustomTextField
        id="tagin-textfield"
        value={value}
        onChange={handleChange}
        placeholder="Tag people"
        inputRef={textareaRef}
        multiline
        rows={1}
        variant="outlined"
        fullWidth
        onFocus={() => {
          setFocused(true);
          setActiveInput("tag");
        }}
        onBlur={() => setFocused(false)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <img
                src={siteConstant?.SOCIAL_ICONS?.PERSON_ICON}
                alt="Person Icon"
                style={{
                  height: "25px",
                  marginRight: "24px",
                }}
              />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              {value.trim().length > 0 ? (
                <CloseIcon
                  className="text-[#674941] cursor-pointer"
                  sx={{ height: 16, width: 16 }}
                  onClick={handleClearText}
                />
              ) : (
                <SearchIcon
                  className="text-[#674941] "
                  sx={{ height: 12, width: 12 }}
                />
              )}
            </InputAdornment>
          ),
        }}
        sx={{
          "& .MuiInputBase-input": {
            color: "black",
            caretColor: "black",
            fontFamily: "inherit",
            fontSize: "inherit",
            padding: "10px 40px",
            whiteSpace: "pre-wrap",
            overflowWrap: "break-word",
          },
          "& fieldset": {
            borderColor: "#D4D6D8",
          },
          "&:hover fieldset": {
            borderColor: "#4582c3",
          },
          "&.Mui-focused fieldset": {
            borderColor: "#4582c3",
          },
          "& .MuiInputBase-input::placeholder": {
            color: "#674941 !important",
          },
          "& .MuiOutlinedInput-input": {
            "&::placeholder": {
              opacity: 1,
            },
          },
        }}
      />

      {showSuggestions && (
        <div
          style={{
            position: "absolute",
            top: "100%",
            left: 0,
            width: "100%",
            backgroundColor: "white",
            zIndex: 1,
            maxHeight: "150px",
            overflowY: "auto",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
        >
          {suggestions.map((suggestion, index) => (
            <MenuItem
              key={index}
              onClick={() => handleSelectSuggestion(suggestion)}
              className="sm:fixed lg:hidden font-Ubuntu"
              sx={{
                display: "flex",
                alignItems: "center",

                "&:hover": {
                  backgroundColor: "#edebec",
                },
              }}
            >
              <img
                src={
                  suggestion.profile
                    ? `${URL.SOCKET_URL}${suggestion.profile}`
                    : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                }
                alt={suggestion.name}
                className={`h-10 w-10 rounded-[14px] border-[2px] p-1 border-Red`}
              />
              <div
                className="ml-2"
                style={{ display: "flex", flexDirection: "column" }}
              >
                <p style={{ margin: 0, fontWeight: "bold", fontSize: "14px" }}>
                  {suggestion.name}
                </p>
                <p style={{ margin: 0, color: "gray", fontSize: "11px" }}>
                  @{suggestion.username}
                </p>
              </div>
            </MenuItem>
          ))}
        </div>
      )}
    </FormControl>
  );
};

export default TagInTextField;
